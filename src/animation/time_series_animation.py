import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import Func<PERSON><PERSON><PERSON>
from matplotlib.ticker import <PERSON><PERSON><PERSON><PERSON>att<PERSON>
from itertools import cycle
import textwrap
from src.logger import get_logger
from src.animation._easing_functions import EASING_FUNCTIONS


LOGGER = get_logger(__name__)


def create_wealth_animation(
    wealth_df: pd.DataFrame,
    investment_years: float | int,
    filename: str = "wealth_animation.mp4",
    duration_sec: int = 8,
    fps: int = 30,
    title: str = "Wealth Projection Over Time",
    easing_function: str = None,
):
    """Create a stylish wealth animation with glowing lines and non-linear pacing.

    Generates an animated visualization of wealth accumulation over time for
    multiple investments. The animation features glowing line effects, smooth
    easing transitions, and a summary table that fades in at the end showing
    final performance metrics.

    Args:
        wealth_df (pd.DataFrame): DataFrame containing wealth data over time.
            Must include a "Total Investments" column and one or more columns
            for individual investment wealth projections.
        investment_years (float | int): Number of years covered by the investment
            period, used for calculating annualized returns.
        filename (str, optional): Output filename for the animation video.
            Defaults to "wealth_animation.mp4".
        duration_sec (int, optional): Duration of the main animation in seconds,
            excluding the summary fade-in period. Defaults to 8.
        fps (int, optional): Frames per second for the animation. Higher values
            create smoother animations but larger file sizes. Defaults to 30.
        title (str, optional): Title displayed at the top of the animation.
            Defaults to "Wealth Projection Over Time".
        easing_function (str, optional): Name of the easing function to apply
            for non-linear animation pacing. Must be a key in EASING_FUNCTIONS
            dictionary. If None, linear pacing is used. Defaults to None.

    Returns:
        None: The function saves the animation to the specified filename and
            logs the creation process.

    Note:
        The animation includes a 2-second summary fade-in period after the main
        animation, showing final rankings, wealth values, CAGR, and volatility
        for each investment.
    """
    LOGGER.activate()

    # --- Data and Animation Setup ---
    investment_col_name = "Total Investments"
    investment_series = wealth_df[investment_col_name]
    stock_wealth_df = wealth_df.drop(columns=[investment_col_name])

    total_investment = investment_series.iloc[-1]

    first_investment_val = investment_series[investment_series > 0].iloc[0]

    SUMMARY_FADE_IN_SEC = 2
    main_animation_frames = duration_sec * fps
    summary_frames = SUMMARY_FADE_IN_SEC * fps
    total_animation_frames = main_animation_frames + summary_frames

    easing_function = EASING_FUNCTIONS.get(easing_function, lambda x: x)
    normalized_time = np.linspace(0, 1, num=main_animation_frames)
    eased_time = np.array([easing_function(t) for t in normalized_time])
    main_frame_indices = (eased_time * (len(wealth_df) - 1)).astype(int)

    # --- Plot Setup ---
    plt.style.use("seaborn-v0_8-darkgrid")
    fig, ax = plt.subplots(figsize=(9, 16), dpi=300)
    fig.patch.set_facecolor("#1a1a1a")
    ax.set_facecolor("#1a1a1a")

    VIBRANT_COLORS = [
        "#1f77b4",
        "#ff7f0e",
        "#2ca02c",
        "#d62728",
        "#9467bd",
        "#8c564b",
        "#e377c2",
        "#7f7f7f",
        "#bcbd22",
        "#17becf",
    ]
    color_map = {
        ticker: color
        for ticker, color in zip(stock_wealth_df.columns, cycle(VIBRANT_COLORS))
    }

    # --- Create line groups for the glow effect ---
    stock_line_groups = {}
    for ticker in stock_wealth_df.columns:
        color = color_map[ticker]
        # Create a "glow" by layering lines with different widths and alphas
        glow_lines = [
            ax.plot([], [], color=color, lw=9, alpha=0.10, zorder=1)[0],
            ax.plot([], [], color=color, lw=6, alpha=0.15, zorder=2)[0],
            ax.plot([], [], color=color, lw=3, alpha=1.0, zorder=3, label=ticker)[0],
        ]
        stock_line_groups[ticker] = glow_lines

    investment_line = ax.plot(
        [], [], lw=2, ls="--", color="white", label=investment_col_name
    )[0]

    # Flatten the list of all lines for the animation manager
    all_stock_lines = [line for group in stock_line_groups.values() for line in group]
    all_lines = all_stock_lines + [investment_line]

    date_text = ax.text(
        0.02,
        0.05,
        "",
        transform=ax.transAxes,
        fontsize=24,
        color="white",
        ha="left",
        va="bottom",
        fontname="monospace",
        weight="bold",
    )

    # --- Styling ---
    wrapped_title = "\n".join(textwrap.wrap(title, width=50))
    ax.set_title(wrapped_title, fontsize=22, color="white", weight="bold", pad=20)

    ax.set_xlabel("Date", fontsize=18, color="white", labelpad=15)
    ax.set_ylabel("Portfolio Value", fontsize=18, color="white", labelpad=15)
    ax.legend(
        loc="upper left",
        bbox_to_anchor=(0.02, 0.98),
        fontsize=14,
        frameon=True,
        facecolor="black",
        edgecolor="white",
        labelcolor="white",
    )
    ax.tick_params(axis="x", colors="white", labelsize=12)
    ax.tick_params(axis="y", colors="white", labelsize=12)
    ax.yaxis.set_major_formatter(FuncFormatter(lambda x, pos: f"${x:,.0f}"))
    ax.grid(alpha=0.25)
    plt.tight_layout(pad=2)

    # --- Final Summary Data ---
    final_wealth = stock_wealth_df.iloc[-1]
    cagr = (final_wealth / total_investment) ** (1 / investment_years) - 1
    cagr_sorted = cagr.sort_values(ascending=False)

    asset_returns = stock_wealth_df.pct_change()
    # Annualize the standard deviation of monthly returns
    volatility = asset_returns.std()

    summary_text_obj = ax.text(
        0.5,
        0.5,
        "",
        transform=ax.transAxes,
        fontsize=14,
        color="white",
        ha="center",
        va="center",
        fontname="monospace",
        alpha=0,
        bbox=dict(boxstyle="round,pad=0.5", fc="#1a1a1a", ec="white", alpha=0),
    )
    summary_artists = [summary_text_obj]

    def update(frame_num):
        if frame_num < main_animation_frames:
            data_idx = main_frame_indices[frame_num]
            current_data = wealth_df.iloc[: data_idx + 1]

            # --- MODIFIED: Update all lines in each glow group ---
            for ticker, line_group in stock_line_groups.items():
                for line in line_group:
                    line.set_data(current_data.index, current_data[ticker])
            investment_line.set_data(
                current_data.index, current_data[investment_col_name]
            )

            y_max = current_data.iloc[:, :-1].max().max() * 1.20
            current_min_wealth = stock_wealth_df.iloc[: data_idx + 1].min().min()
            y_min = min(first_investment_val, current_min_wealth) * 0.95

            ax.set_xlim(
                wealth_df.index.min(), current_data.index.max() + pd.Timedelta(days=90)
            )
            ax.set_ylim(y_min, y_max if y_max > y_min else y_min + 1)
            date_text.set_text(current_data.index.max().strftime("%Y-%m-%d"))
        else:
            if frame_num == main_animation_frames:
                # Calculate dynamic column widths based on actual content
                rank_width = 6
                ticker_width = max(8, max(len(ticker) for ticker in cagr_sorted.index) + 2)
                wealth_width = max(12, max(len(f"${final_wealth[ticker]:,.0f}") for ticker in cagr_sorted.index) + 2)
                cagr_width = 8
                volatility_width = 12

                # Create properly aligned header
                header = (
                    f"{'Rank':<{rank_width}}"
                    f"{'Ticker':<{ticker_width}}"
                    f"{'Final Wealth':>{wealth_width}}"
                    f"{'CAGR':>{cagr_width}}"
                    f"{'Volatility':>{volatility_width}}"
                )
                separator = "=" * len(header)

                # Create body lines with proper alignment
                body_lines = []
                for i, (ticker, cagr_val) in enumerate(cagr_sorted.items()):
                    rank_str = f"#{i+1}"
                    wealth_str = f"${final_wealth[ticker]:,.0f}"
                    cagr_str = f"{cagr_val:.1%}"
                    volatility_str = f"{volatility[ticker]:.1%}"

                    line = (
                        f"{rank_str:<{rank_width}}"
                        f"{ticker:<{ticker_width}}"
                        f"{wealth_str:>{wealth_width}}"
                        f"{cagr_str:>{cagr_width}}"
                        f"{volatility_str:>{volatility_width}}"
                    )
                    body_lines.append(line)

                body = "\n".join(body_lines)

                # Create footer with investment summary
                footer_separator = "-" * len(header)
                footer_string = f"Total Invested: ${total_investment:,.0f}"

                # Calculate total final wealth for additional context
                total_final_wealth = final_wealth.sum()
                total_return_str = f"Total Portfolio Value: ${total_final_wealth:,.0f}"
                overall_return_pct = ((total_final_wealth / total_investment) - 1) * 100
                overall_return_str = f"Overall Return: {overall_return_pct:+.1f}%"

                summary_string = (
                    f"INVESTMENT PERFORMANCE SUMMARY\n"
                    f"{separator}\n"
                    f"{header}\n"
                    f"{separator}\n"
                    f"{body}\n"
                    f"{footer_separator}\n"
                    f"{footer_string}\n"
                    f"{total_return_str}\n"
                    f"{overall_return_str}"
                )

                summary_text_obj.set_text(summary_string)

            fade_in_progress = (frame_num - main_animation_frames) / summary_frames
            alpha_value = min(1.0, fade_in_progress * 1.5)

            summary_text_obj.set_alpha(alpha_value)
            summary_text_obj.get_bbox_patch().set_alpha(min(0.9, alpha_value))

        return all_lines + [date_text] + summary_artists

    # --- Create and Save ---
    LOGGER.info(f"Creating {duration_sec + SUMMARY_FADE_IN_SEC}-second animation ...")
    ani = FuncAnimation(
        fig,
        update,
        frames=total_animation_frames,
        init_func=lambda: all_lines + [date_text] + summary_artists,
        blit=False,
    )
    ani.save(filename, writer="ffmpeg", fps=fps, dpi=300)
    LOGGER.info(f"Animation successfully saved as '{filename}'")
    plt.close(fig)
